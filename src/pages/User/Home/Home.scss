$background-color: rgb(244, 248, 249);
$font-heading-primary: 'NunitoSans-ExtraBold';
$font-color-primary: #13294B;
$font-color-secondary: #25E89F;
$font-heading-secondary: 'ArchitectsDaughter-Regular';
$font-style: 'Inter';
$font-color-tertiary: #2A2A2A;

.home-below{
    // background-color: $background-color;
    background: linear-gradient(180deg, rgb(228, 234, 235) 0%, rgba(255,255,255,1) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 50px 0;
    gap: 2.5rem;
    @media screen and (max-width: 768px){
        padding: 30px 0;
        gap: 1.5rem;
        padding-bottom: 0;
    }
    &-section{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        width: 70%;
        @media screen and (max-width: 768px){
            width: 90%;
        }
        h1{
            font-family: $font-heading-primary;
            font-size: 2.5rem;
            color: $font-color-primary;
            margin-bottom: 20px;
            @media screen and (max-width: 768px){
                font-size: 1.9rem;
            }
            @media screen and (max-width: 586px){
                font-size: 1.6rem;
            }
        }
        p{
            font-family: $font-style;
            font-size: 1.2rem;
            color: $font-color-tertiary;
            margin-bottom: 20px;
            font-weight: 400;
            @media screen and (max-width: 768px){
                font-size: 1rem;
            }
        }
        h2{
            font-family: $font-heading-secondary;
            font-size: 2.5rem;
            color: $font-color-secondary;
            margin-bottom: 20px;
            @media screen and (max-width: 768px){
                font-size: 1.9rem;
            }
            @media screen and (max-width: 586px){
                font-size: 1.6rem;
                font-weight: 700;
            }
        }
        &-features{
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            justify-content: center;
        }
        h3{
            font-family: $font-heading-secondary;
            font-size: 2.2rem;
            color: $font-color-secondary;
            margin-bottom: 20px;
            margin-top: 30px;
            @media screen and (max-width: 768px){
                font-size: 1.6rem;
            }
            @media screen and (max-width: 586px){
                font-size: 1.3rem;
                font-weight: 700;
            }
        }
        &-solutions{
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            justify-content: center;
            margin-bottom: 30px;
        }
    }
}