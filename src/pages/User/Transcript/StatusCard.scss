.status-card{
    display: flex;
    flex-direction: column;
    border-radius: 20px;
    height: 100%;
    // width: 25%;
    // min-width: 40rem;
    // justify-content: center;
    align-items: center;
    padding: 1rem;
    background-color: #fff;
    gap: 2rem;
    h5{
        font-size: 1.5rem;
        font-weight: 600;
        font-family: "Inter", sans-serif;
        color: gray;
        // text-decoration: underline;
    }
    &-INACTIVE{
        border: 2px solid #3987e6;
        .status-card-title{
            background-color: #e0e5ff;
            color: #3987e6;
            padding: 0.4rem 1.8rem;
            border-radius: 50px;
            font-size: 1.4rem;
            font-weight: 600;
            text-align: center;
        }
    }
    &-unavailable{
        border: 2px solid #7239E6;
        .status-card-title {
            background-color: #EAE0FF;
            color: #7239E6;
            padding: 0.4rem 1.8rem;
            border-radius: 50px;
            font-size: 1.4rem;
            font-weight: 600;
        }
    }
    &-outer{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        height: 100%;
        margin-bottom: 2rem;
    }
    &-container{
        display: flex;
        align-items: center;
        justify-content: space-between;
        // padding: 2rem;
        width: 25%;
        position: relative;
        height: 100%;
    }
    &-description{
        // width: 70%;
        text-align: center;
        font-size: 1.1rem;
        font-family: "Inter", sans-serif;
    }
    &-failed,
    &-FAILED{
        border: 2px solid #DC0000;
        .status-card-title{
            background-color: #FFE1E1;
            color: #DC0000;
            padding: 0.4rem 1.8rem;
            border-radius: 50px;
            font-size: 1.4rem;
            font-weight: 600;
        }
    }
    &-successed{
        background-color: #1890FF;
    }
    &-queued{
        border: 2px solid #FF8E00;
        .status-card-title{
            background-color: #FFE6C6;
            color: #FF8E00;
            padding: 0.4rem 1.8rem;
            border-radius: 50px;
            font-size: 1.4rem;
            font-weight: 600;
        }
    }
    &-inprocess{
        border: 2px solid #FFC700;
        .status-card-title{
            background-color: #FDFAC7;
            color: #FFC700;
            padding: 0.4rem 1.8rem;
            border-radius: 50px;
            font-size: 1.4rem;
            font-weight: 600;
        }
    }
    &-null,
    &-undefined{
        border: 2px solid #7239E6;
        width: auto;
        .status-card-title{
            background-color: #EAE0FF;
            color: #7239E6;
            padding: 0.4rem 1.8rem;
            border-radius: 50px;
            font-size: 1.4rem;
            font-weight: 600;
        }
    }
}