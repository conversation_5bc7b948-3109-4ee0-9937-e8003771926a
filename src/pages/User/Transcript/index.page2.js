/* eslint-disable */
import React, { useEffect, useState, useRef } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { Avatar, Spin, Tabs } from "antd";
// import { IoIosVideocam } from "react-icons/io";
import { FaArrowUp, FaDownload } from "react-icons/fa";
import { MdOutlineMessage, MdOutlinePeople } from "react-icons/md";
// import { CgNotes } from "react-icons/cg";
import ReactPlayer from "react-player/lazy";
import { ReactComponent as AskAI } from "./Assets/AskAI.svg";
import { decoder } from "../../../utils";
import { VideoConferenceService } from "../../../services/User/VideoConference/index.service";
import StatusCard from "./StatusCard";
import moment from "moment";
import "./transcription.scss";
import { modalNotification } from "../../../utils";

const formatTime = (seconds) => {
  if (!seconds) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.round(seconds % 60);

  const timeString = `${String(minutes).padStart(2, "0")}:${String(
    remainingSeconds
  ).padStart(2, "0")}`;

  return hours > 0
    ? `${String(hours).padStart(2, "0")}:${timeString}`
    : timeString;
};

const InsightItem = ({ insight, time }) => (
  <li>
    <span className="insight-text">{insight}</span>
    <span className="insight-time">{formatTime(time)}</span>
  </li>
);

const ActionItem = ({ action }) => (
  <li>
    <span className="insight-text">{action?.text}</span>
    <span className="insight-time">{formatTime(action?.start_time)}</span>
  </li>
);

const TranscriptItem = ({ transcript, handleSeek, isActive }) => {
  const startTime = formatTime(transcript?.start_time);
  const timeRange = `${startTime}`;

  const itemRef = useRef(null);

  useEffect(() => {
    if (isActive && itemRef.current) {
      itemRef.current.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  }, [isActive]);

  return (
    <div
      ref={itemRef}
      className={`transcript-content-caption ${
        isActive ? "active-transcript" : ""
      }`}
      onClick={() => handleSeek(transcript?.start_time)}
      style={{ cursor: "pointer" }}
    >
      <span className="transcript-content-caption-time">{timeRange}</span>
      <div className="transcript-content-caption-details">
        <p>{transcript?.transcript}</p>
      </div>
    </div>
  );
};

export default function Transcription({}) {
  const [chatWithAi, setChatWithAi] = useState(false);
  const [keyInsights, setKeyInsights] = useState(null);
  const [summary, setSummary] = useState(null);
  const [actionItems, setActionItems] = useState(null);
  const [transcription, setTranscription] = useState(null);
  const [transcriptionStatus, setTranscriptionStatus] = useState(null);
  const [videoUrl, setVideoUrl] = useState(null);
  const [notes, setNotes] = useState(null);
  const [participants, setParticipants] = useState(null);
  const [loading, setLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isShared, setIsShared] = useState(false);
  const [activeMeetingAnalysisPlan, setActiveMeetingAnalysisPlan] = useState(null);

  const { meetingId } = useParams();
  const [searchParams] = useSearchParams();

  const recordingId = decoder(searchParams.get("recordingId"));
  const sessionId = decoder(searchParams.get("sessionId"));
  const userId = decoder(searchParams.get("userId"));

  // Remove parameters from URL after storing them
  // useEffect(() => {
  //   if (recordingId && sessionId && userId) {
  //     const newUrl = window.location.pathname;
  //     window.history.replaceState({}, document.title, newUrl);
  //   }
  // }, [recordingId, sessionId, userId]);

  const getTranscriptionData = async () => {
    setLoading(true);
    try {
      const response = await VideoConferenceService.getTranscriptionDataService(
        decoder(meetingId),
        recordingId,
        userId
      );
      const { data, success } = response;
      if (success) {
        setTranscription(data?.transcription?.transcription);
        setKeyInsights(data?.takeaways);
        setSummary(data?.summary);
        setActionItems(data?.action_items);
        setNotes(data?.takeaways);
        setTranscriptionStatus(data?.status);
        setVideoUrl(data?.video_url);
        setIsShared(data?.is_share_user !== 0);
        setLoading(false);
        setActiveMeetingAnalysisPlan(data?.active_meeting_analysis_plan === 1 ? "ACTIVE" : "INACTIVE");
      } else {
        setTranscriptionStatus("PLAN_UNAVAILABLE");
      }
    } catch (error) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const getParticipantsLogs = async () => {
    setLoading(true);
    try {
      const response = await VideoConferenceService.getParticipantsLogsService(
        decoder(meetingId),
        sessionId
      );
      const { data, success } = response;
      if (success) {
        setParticipants(data);
        setLoading(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getParticipantsLogs();
  }, []);

  useEffect(() => {
    if (!recordingId || !sessionId) {
      modalNotification({
        type: "error",
        message: "Invalid URL",
      });
    }
  }, [recordingId, sessionId]);

  useEffect(() => {
    getTranscriptionData();
  }, [meetingId, recordingId, userId]);

  const handleSeek = (time) => {
    playerRef.current?.seekTo(time); // static 20 seconds
  };

  const handleProgress = (progress) => {
    setCurrentTime(progress.playedSeconds);
  };

  const moreItems = [
    {
      key: "1",
      label: (
        <div className="transcript-tab">
          <span>Key Insights / Takeaways</span>
        </div>
      ),
      children: (
        <div className="key-insights-content">
          <ul>
            {loading ? (
              <div className="loading-container">
                <Spin />
              </div>
            ) : (
              keyInsights?.map((insight, index) => (
                <InsightItem
                  key={index}
                  insight={insight?.text}
                  time={insight?.start_time}
                />
              ))
            )}
          </ul>
        </div>
      ),
    },
    {
      key: "2",
      label: (
        <div className="transcript-tab">
          <span>Summary</span>
        </div>
      ),
      children: (
        <div className="summary-content">
          <p>{summary}</p>
        </div>
      ),
    },
    {
      key: "3",
      label: (
        <div className="transcript-tab">
          <span>Action Items</span>
        </div>
      ),
      children: (
        <div className="action-items-content">
          <ul>
            {actionItems?.map((action, index) => (
              <ActionItem key={index} action={action} />
            ))}
          </ul>
        </div>
      ),
    },
  ];

  const sideItems = [
    {
      key: "1",
      label: (
        <div className="transcript-tab">
          <MdOutlineMessage />
          <span>Transcript</span>
        </div>
      ),
      children: chatWithAi ? (
        <div className="chat-ai">
          <div className="chat-content">
            <div className="chat-content-message">
              <span>AI:</span>
              <p>
                Hi! I&apos;m AI. How can I help you today? You may ask me
                anything about the meeting.
              </p>
            </div>
          </div>
          <div className="chat-input">
            <input type="text" placeholder="Ask AI" className="chat-input" />
            <FaArrowUp className="chat-input-send" />
          </div>
        </div>
      ) : (
        <div className="transcript-content">
          <div
            className="transcript-content-download"
            onClick={() => {
              downloadTranscript();
            }}
          >
            <FaDownload />
            <span>Download Transcript</span>
          </div>
          <div>
            {loading ? (
              <div className="loading-container">
                <Spin />
              </div>
            ) : (
              transcription?.map((transcript, index) => {
                const isActive =
                  currentTime >= transcript?.start_time &&
                  currentTime <
                    (transcription[index + 1]?.start_time || Infinity);

                return (
                  <TranscriptItem
                    key={index}
                    transcript={transcript}
                    handleSeek={handleSeek}
                    isActive={isActive}
                  />
                );
              })
            )}
          </div>
        </div>
      ),
    },
    // {
    //   key: "2",
    //   label: (
    //     <div className="transcript-tab">
    //       <CgNotes />
    //       <span>Notes</span>
    //     </div>
    //   ),
    //   children: (
    //     <div className="notes-content">
    //       <ul>
    //         <li>
    //           Navigation and mobile responsiveness are critical improvement
    //           areas. <span>15:01 - 18:00</span>
    //         </li>
    //         <li>
    //           Outdated content needs a complete audit and revamp.{" "}
    //           <span>15:01 - 18:00</span>
    //         </li>
    //         <li>
    //           Upgrading the CMS is essential for better performance and SEO.{" "}
    //           <span>15:01 - 18:00</span>
    //         </li>
    //         <li>
    //           User surveys and testing are crucial for a user-friendly redesign.{" "}
    //           <span>15:01 - 18:00</span>
    //         </li>
    //         <li>
    //           Consistency in content tone and visuals is necessary for brand
    //           alignment. <span>15:01 - 18:00</span>
    //         </li>
    //       </ul>
    //     </div>
    //   ),
    // },
    {
      key: "3",
      label: (
        <div className="transcript-tab">
          <MdOutlinePeople />
          <span>Participants</span>
        </div>
      ),
      disabled: !participants,
      children: (
        <div className="participants-content">
          <span className="participants-header">
            Total Participants: ({participants?.peak_participants})
          </span>
          <div
            className="transcript-content-download"
            onClick={() => {
              downloadParticipantLogs();
            }}
          >
            <FaDownload />
            <span>Download Participant Logs</span>
          </div>
          <div className="participants-list-outer">
            {participants?.rows?.map((participant, index) => (
              <div className="participants-list" key={index}>
                <div className="participants-list-item">
                  <Avatar
                    size={40}
                    style={{
                      backgroundColor: `hsl(${
                        (index * 137.5) % 360
                      }, 70%, 65%)`,
                    }}
                  >
                    {participant.screen_name?.[0]}
                  </Avatar>
                  <div className="participants-list-item-details">
                    <span className="participants-list-item-details-name">
                      {participant.screen_name}
                    </span>
                    <div className="participants-list-item-details-role-time">
                      <span className="participants-list-item-details-role">
                        {participant.role
                          ? participant.role.charAt(0).toUpperCase() +
                            participant.role.slice(1)
                          : ""}
                      </span>
                      <div className="participants-list-item-details-time">
                        <span>
                          {moment(participant.joined_at).format("HH:mm")}
                        </span>
                        -
                        <span>
                          {moment(participant.leave_at).format("HH:mm")}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ),
    },
  ];
  const downloadTranscript = () => {
    if (!transcription) return;
    const transcriptData = transcription.map((item) => {
      const cleanedTranscript = item?.transcript.replace(/^,/, ""); // Remove leading comma
      return `${item?.start_time} :\n${cleanedTranscript}\n\n`;
    });

    const blob = new Blob([transcriptData.join("")], { type: "text/plain" }); // Ensure proper formatting
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `transcript_${decoder(meetingId)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadParticipantLogs = () => {
    if (!participants) return;

    // create a csv file from participants data
    const title = "In-Meeting Activities\n\n";
    const headers = "Name,Join Time,Leave Time,Duration,Email,Role\n";
    const participantsData = participants?.rows?.map((participant) => {
      return `${participant?.screen_name},${moment(
        participant?.joined_at
      ).format("HH:mm")},${moment(participant?.leave_at).format("HH:mm")},${
        participant?.duration_formatted
      },${participant?.email},${participant?.role}`;
    });
    const blob = new Blob([title + headers + participantsData.join("\n")], {
      type: "text/csv",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `participants_${decoder(meetingId)}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const onChange = (key) => {
    // console.log(key);
  };

  const playerRef = useRef(null);
  
  return (
    <div
      className={`main ${
        transcriptionStatus === "COMPLETED" || "successed"
          ? "main-successed"
          : ""
      }
      ${transcriptionStatus === "PLAN_UNAVAILABLE" ? "main-not-plan" : ""}
      ${isShared ? "main-shared" : ""}
      ${transcriptionStatus === "failed" ? "main-failed" : ""}
      `}
    >
      <div
        className={`main-left 
          ${isShared ? "main-left-shared" : ""}
          ${
            transcriptionStatus === "COMPLETED" ||
            transcriptionStatus === "successed"
              ? ""
              : "main-left-failed"
          }
        `}
      >
        <div className="main-left-inner">
          {/* <div className="header">
            <div className="header-details">
              <round />
              <span>Hosted by You</span>
            </div>
            <div className="header-details">
              <round />
              <round />
              <round />
              <span>+2 participants</span>
            </div>
          </div> */}

          <div
            className={`video-section ${
              transcriptionStatus === "COMPLETED" || "successed"
                ? "video-successed"
                : ""
            }
              ${isShared ? "video-shared" : ""}
              ${transcriptionStatus === "failed" ? "video-failed" : ""}`}
          >
            {/* <img src={Video} alt="Video Thumbnail" className="video-thumbnail" /> */}
            <ReactPlayer
              ref={playerRef}
              url={videoUrl}
              className="video-thumbnail"
              controls
              playing={false}
              onError={(e) => {
                // console.log(e);
              }}
              onProgress={handleProgress}
              width="100%"
              height="100%"
              config={{
                file: {
                  attributes: {
                    controlsList: "nodownload",
                    disablePictureInPicture: true,
                  },
                },
              }}
            />
          </div>
          {!isShared && (
            transcriptionStatus === "COMPLETED" ||
            transcriptionStatus === "successed"
          ) && <Tabs items={moreItems} className="more-items" />}
          {/* <div className="video-details">
            <div className="video-details-title">
              <IoIosVideocam className="video-details-title-icon" />
              <span>{event_name}</span>
            </div>
            <ul className="video-details-date">
              <li>{event_date}</li>
            </ul>
          </div> */}
        </div>
      </div>
      {!(
        transcriptionStatus === "COMPLETED" ||
        transcriptionStatus === "successed"
      ) && (
        <div className="status-card-container">
          {/* <StatusCard status={activeMeetingAnalysisPlan === "ACTIVE" ? transcriptionStatus : activeMeetingAnalysisPlan} /> */}
          <StatusCard status={
            isShared 
              ? transcriptionStatus
              : (activeMeetingAnalysisPlan === "ACTIVE" 
                ? transcriptionStatus 
                : activeMeetingAnalysisPlan)
          } />
        </div>
      )}

      {(transcriptionStatus === "COMPLETED" ||
        transcriptionStatus === "successed") && (
        <div className="main-right">
          <div className="main-right-inner">
            <Tabs
              defaultActiveKey="1"
              items={sideItems}
              onChange={onChange}
              className={`transcription-tabs ${
                isShared ? "transcription-tabs-shared" : ""
              }`}
            />
            <div
              className="ask-ai"
              onClick={() => {
                setChatWithAi(!chatWithAi);
              }}
            >
              <AskAI className="ask-ai-icon" />
              <span>Ask AI</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
