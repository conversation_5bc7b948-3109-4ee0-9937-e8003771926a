/* Critical CSS for above-the-fold content - Home Page */
/* This should be inlined or loaded with high priority for better LCP */

.homePage {
  .bannerSec {
    overflow: hidden;
    position: relative;
    
    &_bg {
      position: relative;
      width: 100%;
      min-height: 100vh;
      
      @media (max-width: 768px) {
        min-height: 70vh;
      }
      
      @media (max-width: 480px) {
        min-height: 60vh;
      }
    }
    
    .banner-bg-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      z-index: -1;
      will-change: transform;
      transform: translateZ(0);
      backface-visibility: hidden;
    }
    
    &_txt {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      position: relative;
      z-index: 1;
      
      @media (max-width: 768px) {
        min-height: 70vh;
      }
      
      @media (max-width: 480px) {
        min-height: 60vh;
      }
      
      h3 {
        font-size: 48px;
        line-height: 65px;
        margin-bottom: 15px;
        color: white;
        
        @media (max-width: 1200px) {
          font-size: 36px;
          line-height: normal;
        }
        
        @media (max-width: 992px) {
          font-size: 30px;
        }
        
        @media (max-width: 768px) {
          font-size: 24px;
          margin-bottom: 10px;
        }
        
        @media (max-width: 576px) {
          font-size: 20px;
        }
      }
      
      p {
        font-size: 18px;
        line-height: 30px;
        color: #F2F2F2;
        margin-bottom: 30px;
        
        @media (max-width: 1200px) {
          font-size: 16px;
          line-height: normal;
          margin-bottom: 20px;
        }
        
        @media (max-width: 992px) {
          font-size: 14px;
        }
      }
    }
  }
}

/* Optimize button rendering */
.btn-primary {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimize container rendering */
.container {
  will-change: auto;
}

/* Optimize row and column rendering */
.row {
  will-change: auto;
}

[class*="col"] {
  will-change: auto;
}
