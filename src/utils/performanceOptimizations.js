/**
 * Performance optimization utilities for better LCP and overall page performance
 */

/**
 * Preload critical images for better LCP
 * @param {Array} imageUrls - Array of image URLs to preload
 */
export const preloadCriticalImages = (imageUrls) => {
  imageUrls.forEach((url) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    document.head.appendChild(link);
  });
};

/**
 * Optimize image loading with intersection observer
 * @param {string} selector - CSS selector for images to optimize
 */
export const optimizeImageLoading = (selector = '.banner-bg-image') => {
  const images = document.querySelectorAll(selector);
  
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            img.classList.add('loaded');
            observer.unobserve(img);
          }
        }
      });
    });

    images.forEach((img) => imageObserver.observe(img));
  } else {
    // Fallback for browsers without IntersectionObserver
    images.forEach((img) => {
      if (img.dataset.src) {
        img.src = img.dataset.src;
        img.classList.add('loaded');
      }
    });
  }
};

/**
 * Defer non-critical JavaScript execution
 * @param {Function} callback - Function to execute after page load
 * @param {number} delay - Delay in milliseconds (default: 100)
 */
export const deferExecution = (callback, delay = 100) => {
  if (document.readyState === 'complete') {
    setTimeout(callback, delay);
  } else {
    window.addEventListener('load', () => {
      setTimeout(callback, delay);
    });
  }
};

/**
 * Optimize slider performance
 * @param {Object} sliderSettings - Slider configuration object
 * @returns {Object} Optimized slider settings
 */
export const optimizeSliderSettings = (sliderSettings) => {
  return {
    ...sliderSettings,
    lazyLoad: 'ondemand',
    cssEase: 'ease-in-out',
    useTransform: true,
    useCSS: true,
    pauseOnHover: true,
    pauseOnFocus: true,
    accessibility: true,
    adaptiveHeight: false, // Disable for better performance
    variableWidth: false, // Disable for better performance unless needed
  };
};

/**
 * Measure and log LCP for debugging
 */
export const measureLCP = () => {
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
      console.log('LCP Element:', lastEntry.element);
    });
    
    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  }
};

/**
 * Optimize font loading
 */
export const optimizeFontLoading = () => {
  // Add font-display: swap to improve perceived performance
  const style = document.createElement('style');
  style.textContent = `
    @font-face {
      font-family: 'NunitoSans-ExtraBold';
      font-display: swap;
    }
    @font-face {
      font-family: 'ArchitectsDaughter-Regular';
      font-display: swap;
    }
    @font-face {
      font-family: 'Inter';
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
};

/**
 * Reduce layout shifts by setting image dimensions
 * @param {string} selector - CSS selector for images
 */
export const preventLayoutShift = (selector = '.banner-bg-image') => {
  const images = document.querySelectorAll(selector);
  images.forEach((img) => {
    // Set explicit dimensions to prevent layout shift
    if (!img.style.width) img.style.width = '100%';
    if (!img.style.height) img.style.height = '100%';
    if (!img.style.objectFit) img.style.objectFit = 'cover';
  });
};

/**
 * Initialize all performance optimizations
 */
export const initPerformanceOptimizations = () => {
  // Optimize font loading
  optimizeFontLoading();
  
  // Prevent layout shifts
  preventLayoutShift();
  
  // Measure LCP in development
  if (process.env.NODE_ENV === 'development') {
    measureLCP();
  }
  
  // Defer non-critical operations
  deferExecution(() => {
    optimizeImageLoading();
  });
};
